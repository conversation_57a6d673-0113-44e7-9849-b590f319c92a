import { useState } from 'react'
import GlyphIndicator from './components/GlyphIndicator'
import { 
  Home, 
  Settings, 
  Bell, 
  Heart, 
  Star, 
  Zap, 
  Shield, 
  Wifi,
  Battery,
  Volume2
} from 'lucide-react'

function App() {
  const [activeStates, setActiveStates] = useState<Record<string, boolean>>({})

  const toggleState = (key: string) => {
    setActiveStates(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-white text-center mb-12 glow-text">
          Glyph Indicator Showcase
        </h1>
        
        {/* Size Variations */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-white mb-8">Size Variations</h2>
          <div className="flex items-end justify-center gap-8">
            <GlyphIndicator 
              size="sm" 
              label="Small" 
              isActive={activeStates.size_sm}
              onClick={() => toggleState('size_sm')}
            />
            <GlyphIndicator 
              size="md" 
              label="Medium" 
              isActive={activeStates.size_md}
              onClick={() => toggleState('size_md')}
            />
            <GlyphIndicator 
              size="lg" 
              label="Large" 
              isActive={activeStates.size_lg}
              onClick={() => toggleState('size_lg')}
            />
            <GlyphIndicator 
              size="xl" 
              label="Extra Large" 
              isActive={activeStates.size_xl}
              onClick={() => toggleState('size_xl')}
            />
          </div>
        </section>

        {/* Color Variants */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-white mb-8">Color Variants</h2>
          <div className="flex justify-center gap-8 flex-wrap">
            <GlyphIndicator 
              variant="primary" 
              label="Primary" 
              icon={Home}
              isActive={activeStates.variant_primary}
              onClick={() => toggleState('variant_primary')}
            />
            <GlyphIndicator 
              variant="secondary" 
              label="Secondary" 
              icon={Settings}
              isActive={activeStates.variant_secondary}
              onClick={() => toggleState('variant_secondary')}
            />
            <GlyphIndicator 
              variant="accent" 
              label="Accent" 
              icon={Star}
              isActive={activeStates.variant_accent}
              onClick={() => toggleState('variant_accent')}
            />
            <GlyphIndicator 
              variant="warning" 
              label="Warning" 
              icon={Bell}
              isActive={activeStates.variant_warning}
              onClick={() => toggleState('variant_warning')}
            />
            <GlyphIndicator 
              variant="success" 
              label="Success" 
              icon={Heart}
              isActive={activeStates.variant_success}
              onClick={() => toggleState('variant_success')}
            />
          </div>
        </section>

        {/* With Pulse Animation */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-white mb-8">With Pulse Animation</h2>
          <div className="flex justify-center gap-8">
            <GlyphIndicator 
              icon={Zap}
              label="Energy" 
              variant="warning"
              pulse={true}
              isActive={true}
            />
            <GlyphIndicator 
              icon={Shield}
              label="Security" 
              variant="success"
              pulse={true}
              isActive={true}
            />
            <GlyphIndicator 
              icon={Wifi}
              label="Connected" 
              variant="accent"
              pulse={true}
              isActive={true}
            />
          </div>
        </section>

        {/* Interactive Dashboard Example */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-white mb-8">Interactive Dashboard</h2>
          <div className="glass-panel rounded-2xl p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <GlyphIndicator 
                icon={Battery}
                label="Battery" 
                variant="success"
                isActive={activeStates.battery}
                onClick={() => toggleState('battery')}
              />
              <GlyphIndicator 
                icon={Volume2}
                label="Audio" 
                variant="primary"
                isActive={activeStates.audio}
                onClick={() => toggleState('audio')}
              />
              <GlyphIndicator 
                icon={Wifi}
                label="Network" 
                variant="secondary"
                isActive={activeStates.network}
                onClick={() => toggleState('network')}
              />
              <GlyphIndicator 
                icon={Bell}
                label="Alerts" 
                variant="warning"
                pulse={activeStates.alerts}
                isActive={activeStates.alerts}
                onClick={() => toggleState('alerts')}
              />
            </div>
          </div>
        </section>

        {/* Instructions */}
        <section className="text-center">
          <p className="text-gray-300 text-lg">
            Click on any indicator to toggle its state and see the animations in action!
          </p>
        </section>
      </div>
    </div>
  )
}

export default App
