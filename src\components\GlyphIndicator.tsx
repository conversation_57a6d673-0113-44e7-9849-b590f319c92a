import { useState, useEffect } from "react";
import { LucideIcon, DoorClosed, DoorO<PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";

interface GlyphIndicatorProps {
  icon?: LucideIcon;
  label?: string;
  isActive?: boolean;
  onClick?: () => void;
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "primary" | "secondary" | "accent" | "warning" | "success";
  pulse?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: "w-12 h-12",
  md: "w-16 h-16",
  lg: "w-20 h-20",
  xl: "w-24 h-24"
};

const iconSizes = {
  sm: 20,
  md: 24,
  lg: 28,
  xl: 32
};

const GlyphIndicator = ({
  icon: Icon = DoorClosed,
  label,
  isActive = false,
  onClick,
  size = "md",
  variant = "primary",
  pulse = false,
  className
}: GlyphIndicatorProps) => {
  const [internalActive, setInternalActive] = useState(isActive);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    setInternalActive(isActive);
  }, [isActive]);

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      setInternalActive(!internalActive);
    }
  };

  const getVariantClasses = () => {
    if (!internalActive) return "text-gray-600 border-gray-700";
    
    switch (variant) {
      case "primary":
        return "text-white border-white glow-blue";
      case "secondary":
        return "text-blue-400 border-blue-400 glow-blue";
      case "accent":
        return "text-purple-400 border-purple-400 glow-purple";
      case "warning":
        return "text-yellow-400 border-yellow-400 glow-yellow";
      case "success":
        return "text-green-400 border-green-400 glow-green";
      default:
        return "text-white border-white glow-blue";
    }
  };

  // Use door icons for door opening animation
  const DisplayIcon = internalActive ? DoorOpen : DoorClosed;

  return (
    <div className="flex flex-col items-center space-y-2">
      <button
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "relative rounded-full border-2 backdrop-blur-sm transition-all duration-1000 ease-out",
          "hover:scale-110 hover:brightness-125",
          "focus:outline-none focus:ring-2 focus:ring-white/20",
          sizeClasses[size],
          getVariantClasses(),
          pulse && internalActive && "animate-slow-pulse",
          isHovered && "scale-110",
          className
        )}
      >
        {/* Background glow */}
        {internalActive && (
          <div className="absolute inset-0 rounded-full bg-current opacity-10 blur-md scale-150 animate-gentle-pulse" />
        )}
        
        {/* Icon with door opening animation */}
        <div className="relative z-10 w-full h-full flex items-center justify-center">
          <DisplayIcon 
            size={iconSizes[size]} 
            className={cn(
              "transition-all duration-1200 ease-out",
              internalActive ? "drop-shadow-glow rotate-12 scale-105" : "opacity-60 rotate-0 scale-100"
            )}
          />
        </div>

        {/* Hover ring */}
        {isHovered && (
          <div className="absolute inset-0 rounded-full border border-white/30 scale-110 animate-ping" />
        )}
      </button>

      {/* Label */}
      {label && (
        <span className={cn(
          "text-sm font-medium transition-colors duration-300",
          internalActive ? "text-white" : "text-gray-500"
        )}>
          {label}
        </span>
      )}
    </div>
  );
};

export default GlyphIndicator;
